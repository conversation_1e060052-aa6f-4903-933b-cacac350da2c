// 测试In-App Purchase功能的工具文件
import * as InAppPurchases from './inAppPurchases';

export const testInAppPurchaseSetup = async () => {
    console.log('🧪 Testing In-App Purchase Setup...');
    
    try {
        // 1. 检查是否支持In-App Purchase
        const isAvailable = await InAppPurchases.isAvailableAsync();
        console.log('✅ In-App Purchase Available:', isAvailable);
        
        if (!isAvailable) {
            console.log('❌ In-App Purchase not supported on this platform');
            return false;
        }
        
        // 2. 连接到应用商店
        const connected = await InAppPurchases.connectAsync();
        console.log('✅ Connected to App Store:', connected);
        
        if (!connected) {
            console.log('❌ Failed to connect to App Store');
            return false;
        }
        
        // 3. 获取产品信息
        const { results } = await InAppPurchases.getProductsAsync([InAppPurchases.PRODUCT_IDS.PREMIUM]);
        console.log('✅ Products loaded:', results.length);
        
        if (results.length > 0) {
            const product = results[0];
            console.log('📦 Product Details:', {
                id: product.productId,
                title: product.title,
                description: product.description,
                price: product.price
            });
        } else {
            console.log('⚠️ No products found. Make sure the product ID is configured in App Store Connect');
        }
        
        // 4. 检查购买历史
        const purchaseHistory = await InAppPurchases.getPurchaseHistoryAsync();
        console.log('✅ Purchase History:', purchaseHistory.length, 'items');
        
        // 5. 断开连接
        await InAppPurchases.disconnectAsync();
        console.log('✅ Disconnected from App Store');
        
        return true;
        
    } catch (error) {
        console.error('❌ In-App Purchase Test Failed:', error);
        return false;
    }
};

// 在开发环境中可以调用这个函数来测试
export const runInAppPurchaseTest = () => {
    if (__DEV__) {
        testInAppPurchaseSetup();
    }
};
