import { Platform } from 'react-native';

// 条件导入 expo-in-app-purchases，处理原生模块缺失的情况
let InAppPurchases: any;
let isInAppPurchasesAvailable = false;
let initializationError: Error | null = null;

// 安全的模块初始化，带有更好的错误处理
const initializeInAppPurchases = () => {
  try {
    InAppPurchases = require('expo-in-app-purchases');
    isInAppPurchasesAvailable = true;
    console.log('✅ expo-in-app-purchases module loaded successfully');
  } catch (error) {
    initializationError = error instanceof Error ? error : new Error('Unknown initialization error');
    console.warn('⚠️ expo-in-app-purchases native module not available:', initializationError.message);

    // 创建一个安全的模拟对象，防止运行时错误
    InAppPurchases = {
      IAPResponseCode: {
        OK: 0,
        USER_CANCELED: 1,
        ERROR: 2,
        DEFERRED: 3
      },
      IAPErrorCode: {
        UNKNOWN: 0,
        PAYMENT_INVALID: 1,
        PAYMENT_CANCELLED: 2,
        PAYMENT_NOT_ALLOWED: 3,
        STORE_PRODUCT_NOT_AVAILABLE: 4,
        CLOUD_SERVICE_PERMISSION_DENIED: 5,
        CLOUD_SERVICE_NETWORK_CONNECTION_FAILED: 6,
        CLOUD_SERVICE_REVOKED: 7
      },
      // 模拟方法，防止调用时出错
      connectAsync: () => Promise.resolve(),
      disconnectAsync: () => Promise.resolve(),
      getProductsAsync: () => Promise.resolve({ results: [] }),
      purchaseItemAsync: () => Promise.reject(new Error('In-App Purchases not available')),
      setPurchaseListener: () => {},
      getPurchaseHistoryAsync: () => Promise.resolve({ results: [] }),
      finishTransactionAsync: () => Promise.resolve()
    };
  }
};

// 立即初始化
initializeInAppPurchases();

// 导出Expo In-App Purchases的响应码
export const IAPResponseCode = InAppPurchases.IAPResponseCode;

// 产品ID配置
export const PRODUCT_IDS = {
    PREMIUM: Platform.select({
        ios: 'yourae.ninecentspremium',
        android: 'yourae.ninecentspremium',
        default: 'yourae.ninecentspremium'
    })
};

// 类型定义
export interface Product {
    productId: string;
    title: string;
    description: string;
    price: string;
    priceAmountMicros?: string;
    priceCurrencyCode?: string;
}

export interface PurchaseResult {
    responseCode: number;
    results?: any[];
    errorCode?: number;
}

export interface PurchaseListener {
    (result: PurchaseResult): void;
}

// 安全的错误处理包装器
const safeAsyncCall = async <T>(
    operation: () => Promise<T>,
    fallback: T,
    operationName: string
): Promise<T> => {
    try {
        return await operation();
    } catch (error) {
        console.error(`❌ ${operationName} failed:`, error);
        // 确保错误不会导致应用崩溃
        if (error instanceof Error) {
            console.error(`Error details: ${error.message}`);
            console.error(`Stack trace: ${error.stack}`);
        }
        return fallback;
    }
};

// 连接到应用商店
export const connectAsync = async (): Promise<boolean> => {
    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - using mock implementation');
        if (initializationError) {
            console.warn('Initialization error:', initializationError.message);
        }
        return true; // 返回 true 以便应用继续运行
    }

    return safeAsyncCall(
        async () => {
            await InAppPurchases.connectAsync();
            console.log('✅ Connected to app store successfully');
            return true;
        },
        false,
        'Connect to app store'
    );
};

// 断开与应用商店的连接
export const disconnectAsync = async (): Promise<void> => {
    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - nothing to disconnect');
        return;
    }

    await safeAsyncCall(
        async () => {
            await InAppPurchases.disconnectAsync();
            console.log('✅ Disconnected from app store successfully');
        },
        undefined,
        'Disconnect from app store'
    );
};

// 获取产品信息
export const getProductsAsync = async (productIds: string[]): Promise<{ results: Product[] }> => {
    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - returning mock product data');
        // 返回模拟产品数据
        return {
            results: [{
                productId: PRODUCT_IDS.PREMIUM,
                title: 'Premium Features',
                description: 'Unlock all premium features',
                price: '$4.99',
                priceAmountMicros: '4990000',
                priceCurrencyCode: 'USD'
            }]
        };
    }

    return safeAsyncCall(
        async () => {
            const response = await InAppPurchases.getProductsAsync(productIds);
            const results = response?.results || [];

            if (!Array.isArray(results)) {
                console.warn('⚠️ Invalid products response format');
                return { results: [] };
            }

            const products: Product[] = results.map((item: any) => {
                // 安全地处理产品数据，防止空值导致崩溃
                return {
                    productId: item?.productId || '',
                    title: item?.title || 'Premium Features',
                    description: item?.description || 'Unlock all premium features',
                    price: item?.price || '$4.99',
                    priceAmountMicros: item?.priceAmountMicros?.toString() || '4990000',
                    priceCurrencyCode: item?.priceCurrencyCode || 'USD'
                };
            }).filter(product => product.productId); // 过滤掉无效的产品

            console.log(`✅ Successfully loaded ${products.length} products`);
            return { results: products };
        },
        { results: [] },
        'Get products'
    );
};

// 购买产品
export const purchaseItemAsync = async (productId: string): Promise<void> => {
    if (!productId || typeof productId !== 'string') {
        throw new Error('Invalid product ID provided');
    }

    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - simulating purchase');
        // 在模拟环境中，我们可以直接触发成功回调
        return;
    }

    return safeAsyncCall(
        async () => {
            await InAppPurchases.purchaseItemAsync(productId);
            console.log('✅ Purchase initiated successfully for:', productId);
        },
        undefined,
        `Purchase item ${productId}`
    );
};

// 设置购买监听器
export const setPurchaseListener = (listener: PurchaseListener): void => {
    if (!listener || typeof listener !== 'function') {
        console.error('❌ Invalid listener provided to setPurchaseListener');
        return;
    }

    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - purchase listener not set');
        return;
    }

    try {
        InAppPurchases.setPurchaseListener(({ responseCode, results, errorCode }: any) => {
            try {
                // 安全地调用监听器，防止监听器中的错误导致崩溃
                listener({
                    responseCode: responseCode ?? IAPResponseCode.ERROR,
                    results: Array.isArray(results) ? results : [],
                    errorCode: errorCode ?? 0
                });
            } catch (error) {
                console.error('❌ Error in purchase listener:', error);
            }
        });
        console.log('✅ Purchase listener set successfully');
    } catch (error) {
        console.error('❌ Failed to set purchase listener:', error);
    }
};

// 获取购买历史（用于恢复购买）
export const getPurchaseHistoryAsync = async (): Promise<any[]> => {
    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - returning empty purchase history');
        return [];
    }

    return safeAsyncCall(
        async () => {
            const response = await InAppPurchases.getPurchaseHistoryAsync();
            const results = response?.results || [];

            if (!Array.isArray(results)) {
                console.warn('⚠️ Invalid purchase history response format');
                return [];
            }

            console.log(`✅ Retrieved ${results.length} purchase history items`);
            return results;
        },
        [],
        'Get purchase history'
    );
};

// 完成交易（防止重复购买提示）
export const finishTransactionAsync = async (purchase: any): Promise<void> => {
    if (!isInAppPurchasesAvailable) {
        console.warn('⚠️ In-App Purchases not available - cannot finish transaction');
        return;
    }

    if (!purchase) {
        console.warn('⚠️ No purchase data provided to finish transaction');
        return;
    }

    await safeAsyncCall(
        async () => {
            await InAppPurchases.finishTransactionAsync(purchase);
            console.log('✅ Transaction finished successfully');
        },
        undefined,
        'Finish transaction'
    );
};

// 检查模块可用性
export const isAvailableAsync = async (): Promise<boolean> => {
    return isInAppPurchasesAvailable;
};

// 获取初始化错误信息
export const getInitializationError = (): Error | null => {
    return initializationError;
};